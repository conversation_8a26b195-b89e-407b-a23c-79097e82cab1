import numpy as np
from scipy.io.wavfile import write as write_wav
import os

# Parameters for sound generation
sample_rate = 44100  # samples per second
duration_eat = 0.1   # seconds
duration_gameover = 0.5 # seconds
duration_crawl = 0.1 # seconds (Adjust duration as needed)
frequency_eat = 1000 # Hz (a simple beep)
frequency_gameover_start = 400 # Hz (lower pitch)
frequency_gameover_end = 200 # Hz (pitch descends)
frequency_crawl = 150 # Hz (Low frequency for a deep sound)
volume = 0.5 # 0.0 to 1.0
volume_crawl = 0.2 # Volume for crawl sound (adjust as needed)

def generate_sine_wave(frequency, duration, sample_rate, volume):
    t = np.linspace(0., duration, int(sample_rate * duration))
    amplitude = np.iinfo(np.int16).max * volume
    data = amplitude * np.sin(2. * np.pi * frequency * t)
    return data.astype(np.int16)

def generate_descending_sine_wave(start_freq, end_freq, duration, sample_rate, volume):
    t = np.linspace(0., duration, int(sample_rate * duration))
    amplitude = np.iinfo(np.int16).max * volume
    # Simple linear frequency change
    frequencies = np.linspace(start_freq, end_freq, len(t))
    data = amplitude * np.sin(2. * np.pi * frequencies * t)
    return data.astype(np.int16)

def generate_footstep_sound(duration, sample_rate, volume):
    # Generate a short burst of white noise, shaped with an envelope to sound like a footstep
    t = np.linspace(0., duration, int(sample_rate * duration))
    amplitude = np.iinfo(np.int16).max * volume
    # Envelope: quick attack, fast decay
    envelope = np.exp(-5 * t / duration)
    noise = np.random.normal(0, 1, len(t))
    data = amplitude * envelope * noise
    return data.astype(np.int16)

# Generate eat sound (simple beep)
eat_data = generate_sine_wave(frequency_eat, duration_eat, sample_rate, volume)
eat_filename = "eat.wav"
write_wav(eat_filename, sample_rate, eat_data)
print(f"Generated {eat_filename}")

# Generate game over sound (descending tone)
gameover_data = generate_descending_sine_wave(frequency_gameover_start, frequency_gameover_end, duration_gameover, sample_rate, volume)
gameover_filename = "gameover.wav"
write_wav(gameover_filename, sample_rate, gameover_data)
print(f"Generated {gameover_filename}")

# Generate crawl sound (low frequency)
crawl_data = generate_sine_wave(frequency_crawl, duration_crawl, sample_rate, volume_crawl) # Use generate_sine_wave with new low frequency
crawl_filename = "crawl.wav"
write_wav(crawl_filename, sample_rate, crawl_data)
print(f"Generated {crawl_filename}")


print("Sound files generated. Make sure they are in the same directory as your main.py.")
print("Run this script (generate_sounds.py) to update the sound files.")
gameover_filename = "gameover.wav"
write_wav(gameover_filename, sample_rate, gameover_data)
print(f"Generated {gameover_filename}")

# Generate crawl sound (footstep-like noise)
crawl_data = generate_footstep_sound(duration_crawl, sample_rate, volume_crawl) # Use generate_footstep_sound
crawl_filename = "crawl.wav"
write_wav(crawl_filename, sample_rate, crawl_data)
print(f"Generated {crawl_filename}")


print("Sound files generated. Make sure they are in the same directory as your main.py.")
print("Run this script (generate_sounds.py) to update the sound files.")
