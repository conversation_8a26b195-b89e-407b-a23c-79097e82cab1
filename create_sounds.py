#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create sound files for the Snake game
This script generates WAV files for various game sounds
"""

import numpy as np
import wave
import struct

def create_sound_file(filename, wave_data, sample_rate=22050):
    """Create a WAV file from wave data"""
    # Normalize and convert to 16-bit integers
    wave_data = np.clip(wave_data, -1.0, 1.0)
    wave_data = (wave_data * 32767).astype(np.int16)
    
    # Create stereo sound
    if len(wave_data.shape) == 1:
        stereo_data = np.array([wave_data, wave_data]).T
    else:
        stereo_data = wave_data
    
    # Write WAV file
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(2)  # Stereo
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        
        # Convert to bytes and write
        for frame in stereo_data:
            wav_file.writeframes(struct.pack('<hh', frame[0], frame[1]))

def create_eat_sound():
    """Create eating sound - high-pitched beep with decay"""
    sample_rate = 22050
    duration = 0.15
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Multiple frequency components for richer sound
    freq1 = 800
    freq2 = 1200
    wave = (np.sin(2 * np.pi * freq1 * t) * 0.6 + 
            np.sin(2 * np.pi * freq2 * t) * 0.4) * np.exp(-t * 8)
    
    create_sound_file("eat.wav", wave)
    print("Created eat.wav")

def create_gameover_sound():
    """Create game over sound - descending tone"""
    sample_rate = 22050
    duration = 1.0
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Descending frequency
    start_freq = 400
    end_freq = 100
    frequency = start_freq - (start_freq - end_freq) * t / duration
    
    wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 1.5)
    
    create_sound_file("gameover.wav", wave)
    print("Created gameover.wav")

def create_move_sound():
    """Create movement sound - subtle tick"""
    sample_rate = 22050
    duration = 0.03
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    frequency = 150
    wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 30)
    
    create_sound_file("move.wav", wave)
    print("Created move.wav")

def create_turn_sound():
    """Create turn sound - quick chirp"""
    sample_rate = 22050
    duration = 0.08
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    frequency = 600
    wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 15)
    
    create_sound_file("turn.wav", wave)
    print("Created turn.wav")

def create_start_sound():
    """Create start game sound - rising tone"""
    sample_rate = 22050
    duration = 0.6
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Rising frequency
    start_freq = 300
    end_freq = 600
    frequency = start_freq + (end_freq - start_freq) * t / duration
    
    wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 2)
    
    create_sound_file("start.wav", wave)
    print("Created start.wav")

def create_crawl_sound():
    """Create crawl sound for backward compatibility"""
    # Same as move sound
    create_move_sound()
    # Copy move.wav to crawl.wav
    import shutil
    shutil.copy("move.wav", "crawl.wav")
    print("Created crawl.wav")

if __name__ == "__main__":
    print("Creating sound files for Snake game...")
    
    try:
        create_eat_sound()
        create_gameover_sound() 
        create_move_sound()
        create_turn_sound()
        create_start_sound()
        create_crawl_sound()
        
        print("\nAll sound files created successfully!")
        print("Files created:")
        print("- eat.wav")
        print("- gameover.wav")
        print("- move.wav")
        print("- turn.wav")
        print("- start.wav")
        print("- crawl.wav")
        
    except Exception as e:
        print(f"Error creating sound files: {e}")
