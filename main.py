import tkinter as tk
import random
import datetime # Import datetime module
import pygame.mixer # Import pygame mixer for sound

# Game constants
GAME_WIDTH = 350 # Reduced from 700
GAME_HEIGHT = 350 # Reduced from 700
SPEED = 150 # Increased from 100 to slow down the game/crawl rhythm
SPACE_SIZE = 25 # Reduced from 50
BODY_PARTS = 3
SNAKE_COLOR = "#00FF00"
FOOD_COLOR = "#FF0000"
BACKGROUND_COLOR = "#000000"
SCORE_COLOR = "#FFFFFF"

# Global variables
score = 0
direction = 'down'
score_history = [] # List to store score history
game_start_time = None # Variable to store the start time of the current game

# Initialize mixer and load sounds
pygame.mixer.init()
try:
    eat_sound = pygame.mixer.Sound("eat.wav") # Make sure you have eat.wav
    gameover_sound = pygame.mixer.Sound("gameover.wav") # Make sure you have gameover.wav
    crawl_sound = pygame.mixer.Sound("crawl.wav") # Make sure you have crawl.wav
except pygame.error as e:
    print(f"Could not load sound files: {e}")
    eat_sound = None
    gameover_sound = None
    crawl_sound = None


class Snake:
    def __init__(self):
        self.body_size = BODY_PARTS
        self.coordinates = []
        self.squares = []

        for i in range(0, BODY_PARTS):
            self.coordinates.append([0, 0])

        for x, y in self.coordinates:
            square = canvas.create_rectangle(x, y, x + SPACE_SIZE, y + SPACE_SIZE, fill=SNAKE_COLOR, tag="snake")
            self.squares.append(square)

class Food:
    def __init__(self):
        x = random.randint(0, (GAME_WIDTH // SPACE_SIZE) - 1) * SPACE_SIZE
        y = random.randint(0, (GAME_HEIGHT // SPACE_SIZE) - 1) * SPACE_SIZE
        self.coordinates = [x, y]
        canvas.create_oval(x, y, x + SPACE_SIZE, y + SPACE_SIZE, fill=FOOD_COLOR, tag="food")

def next_turn(snake, food):
    x, y = snake.coordinates[0]

    if direction == "up":
        y -= SPACE_SIZE
    elif direction == "down":
        y += SPACE_SIZE
    elif direction == "left":
        x -= SPACE_SIZE
    elif direction == "right":
        x += SPACE_SIZE

    snake.coordinates.insert(0, [x, y])

    square = canvas.create_rectangle(x, y, x + SPACE_SIZE, y + SPACE_SIZE, fill=SNAKE_COLOR)
    snake.squares.insert(0, square)

    if x == food.coordinates[0] and y == food.coordinates[1]:
        global score
        score += 1
        label.config(text="Score:{}".format(score))
        canvas.delete("food")
        food = Food()
        # Play eat sound
        if eat_sound:
            eat_sound.play()
    else:
        del snake.coordinates[-1]
        canvas.delete(snake.squares[-1])
        del snake.squares[-1]

    if check_collisions(snake):
        game_over()
    else:
        # Play crawl sound
        if crawl_sound:
            crawl_sound.play()
        window.after(SPEED, next_turn, snake, food) # SPEED controls the delay

def change_direction(new_direction):
    global direction
    if new_direction == 'left':
        if direction != 'right':
            direction = new_direction
    elif new_direction == 'right':
        if direction != 'left':
            direction = new_direction
    elif new_direction == 'up':
        if direction != 'down':
            direction = new_direction
    elif new_direction == 'down':
        if direction != 'up':
            direction = new_direction

def check_collisions(snake):
    x, y = snake.coordinates[0]

    if x < 0 or x >= GAME_WIDTH or y < 0 or y >= GAME_HEIGHT:
        return True

    for body_part in snake.coordinates[1:]:
        if x == body_part[0] and y == body_part[1]:
            return True

    return False

def game_over():
    # Play game over sound
    if gameover_sound:
        gameover_sound.play()

    canvas.delete(tk.ALL)
    canvas.create_text(canvas.winfo_width()/2, canvas.winfo_height()/2,
                       font=('consolas', 35), text="GAME OVER", fill="red", tag="gameover") # Reduced font size from 70
    # Show the button frame again
    button_frame.pack() # Pack the frame containing the buttons
    # Record the score history
    game_end_time = datetime.datetime.now() # Record end time
    score_history.append({'score': score, 'start_time': game_start_time, 'end_time': game_end_time})
    # print("Score History:", score_history) # Remove console print

def start_game():
    global snake, food, score, direction, game_start_time
    # Reset score and direction
    score = 0
    direction = 'down'
    label.config(text="Score:{}".format(score))

    # Clear previous game elements if any
    canvas.delete(tk.ALL)

    # Hide the button frame
    button_frame.pack_forget() # Unpack the frame containing the buttons

    # Record the start time
    game_start_time = datetime.datetime.now()

    # Create new snake and food
    snake = Snake()
    food = Food()

    # Start the game loop
    next_turn(snake, food)

def show_history():
    history_window = tk.Toplevel(window)
    history_window.title("Score History")
    history_window.geometry("400x300") # Keep history window size reasonable

    history_text = tk.Text(history_window, wrap="word")
    history_text.pack(expand=True, fill="both")

    history_text.insert(tk.END, "--- Game History ---\n\n")

    if not score_history:
        history_text.insert(tk.END, "No games played yet.")
    else:
        for i, game_data in enumerate(score_history):
            start_str = game_data['start_time'].strftime("%Y-%m-%d %H:%M:%S") if game_data['start_time'] else "N/A"
            end_str = game_data['end_time'].strftime("%Y-%m-%d %H:%M:%S") if game_data['end_time'] else "N/A"
            history_text.insert(tk.END, f"Game {i+1}:\n")
            history_text.insert(tk.END, f"  Score: {game_data['score']}\n")
            history_text.insert(tk.END, f"  Start: {start_str}\n")
            history_text.insert(tk.END, f"  End:   {end_str}\n")
            history_text.insert(tk.END, "--------------------\n")

    history_text.config(state="disabled") # Make the text widget read-only

# Setup the main window
window = tk.Tk()
window.title("Snake game")
window.resizable(False, False)

label = tk.Label(window, text="Score:{}".format(score), font=('consolas', 20), fg=SCORE_COLOR, bg=BACKGROUND_COLOR) # Reduced font size from 40
label.pack()

canvas = tk.Canvas(window, bg=BACKGROUND_COLOR, height=GAME_HEIGHT, width=GAME_WIDTH)
canvas.pack()

# Create a frame for the buttons
button_frame = tk.Frame(window)
button_frame.pack() # Pack the frame into the main window

# Add a start button inside the frame
start_button = tk.Button(button_frame, text="Start Game", font=('consolas', 15), command=start_game)
start_button.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

# Add a show history button inside the frame
history_button = tk.Button(button_frame, text="Show History", font=('consolas', 15), command=show_history) # Changed font size to 15
history_button.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)


window.update()

window_width = window.winfo_width()
window_height = window.winfo_height()
screen_width = window.winfo_screenwidth()
screen_height = window.winfo_screenheight()

x = int((screen_width/2) - (window_width/2))
y = int((screen_height/2) - (window_height/2))

window.geometry(f"{window_width}x{window_height}+{x}+{y}")

window.bind('<Left>', lambda event: change_direction('left'))
window.bind('<Right>', lambda event: change_direction('right'))
window.bind('<Up>', lambda event: change_direction('up'))
window.bind('<Down>', lambda event: change_direction('down'))
# Bind the Enter key to the start_game function
window.bind('<Return>', lambda event: start_game())

# Do not start the game immediately, wait for button click
# snake = Snake()
# food = Food()
# next_turn(snake, food)

window.mainloop()
