import tkinter as tk
from tkinter import ttk
import random
import datetime # Import datetime module
import pygame.mixer # Import pygame mixer for sound

# Game constants
GAME_WIDTH = 400 # Slightly increased for better proportions
GAME_HEIGHT = 400 # Slightly increased for better proportions
SPEED = 150 # Increased from 100 to slow down the game/crawl rhythm
SPACE_SIZE = 20 # Adjusted for better grid
BODY_PARTS = 3

# Modern color scheme
SNAKE_HEAD_COLOR = "#4CAF50"  # Material Green
SNAKE_BODY_COLOR = "#66BB6A"  # Lighter Green
FOOD_COLOR = "#FF5722"        # Material Deep Orange
BACKGROUND_COLOR = "#1A1A2E"  # Dark Blue-Purple
GRID_COLOR = "#16213E"        # Slightly lighter for grid
SCORE_COLOR = "#FFFFFF"       # White
ACCENT_COLOR = "#0F3460"      # Blue accent
BUTTON_COLOR = "#E94560"      # Pink-Red for buttons
BUTTON_HOVER_COLOR = "#C73650" # Darker pink-red for hover

# Global variables
score = 0
direction = 'down'
score_history = [] # List to store score history
game_start_time = None # Variable to store the start time of the current game

# Sound management class
class SoundManager:
    def __init__(self):
        self.sounds_enabled = True
        self.volume = 0.7
        self.sounds = {}
        self.init_mixer()
        self.load_sounds()

    def init_mixer(self):
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            pygame.mixer.set_num_channels(8)  # Allow multiple sounds to play simultaneously
        except pygame.error as e:
            print(f"Could not initialize mixer: {e}")
            self.sounds_enabled = False

    def load_sounds(self):
        """Load sound files or create synthetic sounds if files don't exist"""
        sound_files = {
            'eat': 'eat.wav',
            'gameover': 'gameover.wav',
            'move': 'move.wav',
            'turn': 'turn.wav',
            'start': 'start.wav'
        }

        for sound_name, filename in sound_files.items():
            try:
                sound = pygame.mixer.Sound(filename)
                sound.set_volume(self.volume)
                self.sounds[sound_name] = sound
            except (pygame.error, FileNotFoundError):
                # Create synthetic sounds if files don't exist
                self.sounds[sound_name] = self.create_synthetic_sound(sound_name)

    def create_synthetic_sound(self, sound_type):
        """Create synthetic sounds using pygame"""
        try:
            import numpy as np

            sample_rate = 22050

            if sound_type == 'eat':
                # High-pitched beep for eating
                duration = 0.1
                frequency = 800
                t = np.linspace(0, duration, int(sample_rate * duration))
                wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 10)

            elif sound_type == 'gameover':
                # Descending tone for game over
                duration = 0.8
                t = np.linspace(0, duration, int(sample_rate * duration))
                frequency = 400 - 200 * t / duration
                wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 2)

            elif sound_type == 'move':
                # Subtle tick for movement
                duration = 0.05
                frequency = 200
                t = np.linspace(0, duration, int(sample_rate * duration))
                wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 20)

            elif sound_type == 'turn':
                # Quick chirp for direction change
                duration = 0.08
                frequency = 600
                t = np.linspace(0, duration, int(sample_rate * duration))
                wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 15)

            elif sound_type == 'start':
                # Rising tone for game start
                duration = 0.5
                t = np.linspace(0, duration, int(sample_rate * duration))
                frequency = 300 + 200 * t / duration
                wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 3)

            else:
                return None

            # Convert to pygame sound format
            wave = (wave * 32767).astype(np.int16)
            stereo_wave = np.array([wave, wave]).T
            sound = pygame.sndarray.make_sound(stereo_wave)
            sound.set_volume(self.volume)
            return sound

        except ImportError:
            # If numpy is not available, create simple beep
            return self.create_simple_beep(sound_type)
        except Exception as e:
            print(f"Could not create synthetic sound for {sound_type}: {e}")
            return None

    def create_simple_beep(self, sound_type):
        """Create simple beep sounds without numpy"""
        try:
            sample_rate = 22050
            duration = 0.1
            frequency = 440

            if sound_type == 'eat':
                frequency = 800
            elif sound_type == 'gameover':
                frequency = 200
                duration = 0.5
            elif sound_type == 'move':
                frequency = 150
                duration = 0.03
            elif sound_type == 'turn':
                frequency = 600
                duration = 0.05
            elif sound_type == 'start':
                frequency = 500
                duration = 0.3

            frames = int(duration * sample_rate)
            arr = []
            for i in range(frames):
                time = float(i) / sample_rate
                wave = int(4096 * (i / frames) * (1 - i / frames) *
                          (1 if int(2 * frequency * time) % 2 else -1))
                arr.append([wave, wave])

            sound = pygame.sndarray.make_sound(arr)
            sound.set_volume(self.volume)
            return sound
        except Exception as e:
            print(f"Could not create simple beep for {sound_type}: {e}")
            return None

    def play_sound(self, sound_name):
        """Play a sound by name"""
        if not self.sounds_enabled or sound_name not in self.sounds:
            return

        sound = self.sounds[sound_name]
        if sound:
            try:
                sound.play()
            except pygame.error as e:
                print(f"Could not play sound {sound_name}: {e}")

    def set_volume(self, volume):
        """Set volume for all sounds (0.0 to 1.0)"""
        self.volume = max(0.0, min(1.0, volume))
        for sound in self.sounds.values():
            if sound:
                sound.set_volume(self.volume)

    def toggle_sounds(self):
        """Toggle sound on/off"""
        self.sounds_enabled = not self.sounds_enabled
        return self.sounds_enabled

# Initialize sound manager
sound_manager = SoundManager()


class Snake:
    def __init__(self):
        self.body_size = BODY_PARTS
        self.coordinates = []
        self.squares = []

        for i in range(0, BODY_PARTS):
            self.coordinates.append([0, 0])

        for i, (x, y) in enumerate(self.coordinates):
            # Head is different color from body
            color = SNAKE_HEAD_COLOR if i == 0 else SNAKE_BODY_COLOR
            square = canvas.create_rectangle(x, y, x + SPACE_SIZE, y + SPACE_SIZE,
                                           fill=color, outline="#2E7D32", width=1, tag="snake")
            self.squares.append(square)

class Food:
    def __init__(self):
        x = random.randint(0, (GAME_WIDTH // SPACE_SIZE) - 1) * SPACE_SIZE
        y = random.randint(0, (GAME_HEIGHT // SPACE_SIZE) - 1) * SPACE_SIZE
        self.coordinates = [x, y]
        # Create a more attractive food with gradient effect
        canvas.create_oval(x + 2, y + 2, x + SPACE_SIZE - 2, y + SPACE_SIZE - 2,
                          fill=FOOD_COLOR, outline="#D84315", width=2, tag="food")
        # Add a small highlight for 3D effect
        canvas.create_oval(x + 5, y + 5, x + 10, y + 10,
                          fill="#FFAB91", outline="", tag="food")

def next_turn(snake, food):
    x, y = snake.coordinates[0]

    if direction == "up":
        y -= SPACE_SIZE
    elif direction == "down":
        y += SPACE_SIZE
    elif direction == "left":
        x -= SPACE_SIZE
    elif direction == "right":
        x += SPACE_SIZE

    snake.coordinates.insert(0, [x, y])

    # Create new head with head color and update previous head to body color
    square = canvas.create_rectangle(x, y, x + SPACE_SIZE, y + SPACE_SIZE,
                                   fill=SNAKE_HEAD_COLOR, outline="#2E7D32", width=1)
    snake.squares.insert(0, square)

    # Update the previous head to body color if it exists
    if len(snake.squares) > 1:
        canvas.itemconfig(snake.squares[1], fill=SNAKE_BODY_COLOR)

    if x == food.coordinates[0] and y == food.coordinates[1]:
        global score
        score += 1
        label.config(text="Score:{}".format(score))
        canvas.delete("food")
        food = Food()
        # Play eat sound
        sound_manager.play_sound('eat')
    else:
        del snake.coordinates[-1]
        canvas.delete(snake.squares[-1])
        del snake.squares[-1]

    if check_collisions(snake):
        game_over()
    else:
        # Play move sound
        sound_manager.play_sound('move')
        window.after(SPEED, next_turn, snake, food) # SPEED controls the delay

def change_direction(new_direction):
    global direction
    old_direction = direction

    if new_direction == 'left':
        if direction != 'right':
            direction = new_direction
    elif new_direction == 'right':
        if direction != 'left':
            direction = new_direction
    elif new_direction == 'up':
        if direction != 'down':
            direction = new_direction
    elif new_direction == 'down':
        if direction != 'up':
            direction = new_direction

    # Play turn sound if direction actually changed
    if old_direction != direction:
        sound_manager.play_sound('turn')

def check_collisions(snake):
    x, y = snake.coordinates[0]

    if x < 0 or x >= GAME_WIDTH or y < 0 or y >= GAME_HEIGHT:
        return True

    for body_part in snake.coordinates[1:]:
        if x == body_part[0] and y == body_part[1]:
            return True

    return False

def game_over():
    # Play game over sound
    sound_manager.play_sound('gameover')

    canvas.delete(tk.ALL)

    # Create a more attractive game over screen
    # Background overlay
    canvas.create_rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT,
                           fill="#000000", stipple="gray50", tag="gameover")

    # Game Over text with shadow effect
    canvas.create_text(canvas.winfo_width()/2 + 2, canvas.winfo_height()/2 - 48,
                       font=('Arial', 32, 'bold'), text="GAME OVER", fill="#333333", tag="gameover")
    canvas.create_text(canvas.winfo_width()/2, canvas.winfo_height()/2 - 50,
                       font=('Arial', 32, 'bold'), text="GAME OVER", fill="#FF5722", tag="gameover")

    # Score display
    canvas.create_text(canvas.winfo_width()/2, canvas.winfo_height()/2 - 10,
                       font=('Arial', 18, 'bold'), text=f"Final Score: {score}",
                       fill="#FFFFFF", tag="gameover")

    # Instructions
    canvas.create_text(canvas.winfo_width()/2, canvas.winfo_height()/2 + 20,
                       font=('Arial', 12), text="Press Enter or click Start Game to play again",
                       fill="#CCCCCC", tag="gameover")

    # Show the button frame again
    button_frame.pack() # Pack the frame containing the buttons
    # Record the score history
    game_end_time = datetime.datetime.now() # Record end time
    score_history.append({'score': score, 'start_time': game_start_time, 'end_time': game_end_time})

def start_game():
    global snake, food, score, direction, game_start_time
    # Play start game sound
    sound_manager.play_sound('start')

    # Reset score and direction
    score = 0
    direction = 'down'
    label.config(text="Score: {}".format(score))

    # Clear previous game elements if any
    canvas.delete(tk.ALL)

    # Redraw the grid
    draw_grid()

    # Hide the button frame
    button_frame.pack_forget() # Unpack the frame containing the buttons

    # Record the start time
    game_start_time = datetime.datetime.now()

    # Create new snake and food
    snake = Snake()
    food = Food()

    # Start the game loop
    next_turn(snake, food)

def show_history():
    history_window = tk.Toplevel(window)
    history_window.title("📊 Game History")
    history_window.geometry("500x400")
    history_window.configure(bg=BACKGROUND_COLOR)
    history_window.resizable(False, False)

    # Center the history window
    history_window.transient(window)
    history_window.grab_set()

    # Title for history window
    title_frame = tk.Frame(history_window, bg=BACKGROUND_COLOR)
    title_frame.pack(pady=10)

    title_label = tk.Label(title_frame, text="📊 GAME HISTORY",
                          font=('Arial', 20, 'bold'),
                          fg="#4CAF50", bg=BACKGROUND_COLOR)
    title_label.pack()

    # Create a frame for the text widget with scrollbar
    text_frame = tk.Frame(history_window, bg=BACKGROUND_COLOR)
    text_frame.pack(expand=True, fill="both", padx=20, pady=10)

    # Scrollbar
    scrollbar = tk.Scrollbar(text_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # Text widget with better styling
    history_text = tk.Text(text_frame, wrap="word",
                          bg="#2A2A3E", fg="#FFFFFF",
                          font=('Consolas', 11),
                          yscrollcommand=scrollbar.set,
                          relief='flat', bd=0,
                          padx=15, pady=15)
    history_text.pack(expand=True, fill="both")

    scrollbar.config(command=history_text.yview)

    if not score_history:
        history_text.insert(tk.END, "🎮 No games played yet!\n\n")
        history_text.insert(tk.END, "Start playing to see your game history here.")
    else:
        history_text.insert(tk.END, f"🏆 Total Games Played: {len(score_history)}\n")
        best_score = max(game['score'] for game in score_history)
        history_text.insert(tk.END, f"🥇 Best Score: {best_score}\n")
        avg_score = sum(game['score'] for game in score_history) / len(score_history)
        history_text.insert(tk.END, f"📈 Average Score: {avg_score:.1f}\n\n")
        history_text.insert(tk.END, "=" * 50 + "\n\n")

        for i, game_data in enumerate(score_history):
            start_str = game_data['start_time'].strftime("%Y-%m-%d %H:%M:%S") if game_data['start_time'] else "N/A"
            end_str = game_data['end_time'].strftime("%Y-%m-%d %H:%M:%S") if game_data['end_time'] else "N/A"

            # Calculate game duration
            if game_data['start_time'] and game_data['end_time']:
                duration = game_data['end_time'] - game_data['start_time']
                duration_str = str(duration).split('.')[0]  # Remove microseconds
            else:
                duration_str = "N/A"

            history_text.insert(tk.END, f"🎯 Game #{i+1}\n")
            history_text.insert(tk.END, f"   Score: {game_data['score']} points\n")
            history_text.insert(tk.END, f"   Started: {start_str}\n")
            history_text.insert(tk.END, f"   Ended: {end_str}\n")
            history_text.insert(tk.END, f"   Duration: {duration_str}\n")
            history_text.insert(tk.END, "-" * 40 + "\n\n")

    history_text.config(state="disabled")

    # Close button
    close_button = tk.Button(history_window, text="✖ Close",
                           command=history_window.destroy,
                           **button_style)
    close_button.pack(pady=10)

# Setup the main window
window = tk.Tk()
window.title("🐍 Snake Game - Modern Edition")
window.resizable(False, False)
window.configure(bg=BACKGROUND_COLOR)

# Create a title frame
title_frame = tk.Frame(window, bg=BACKGROUND_COLOR)
title_frame.pack(pady=10)

# Add a stylish title
title_label = tk.Label(title_frame, text="🐍 SNAKE GAME",
                      font=('Arial', 24, 'bold'),
                      fg="#4CAF50", bg=BACKGROUND_COLOR)
title_label.pack()

# Score label with better styling
label = tk.Label(window, text="Score: {}".format(score),
                font=('Arial', 18, 'bold'),
                fg=SCORE_COLOR, bg=BACKGROUND_COLOR,
                pady=5)
label.pack()

# Create canvas with border effect
canvas_frame = tk.Frame(window, bg="#2E7D32", padx=3, pady=3)
canvas_frame.pack(pady=5)

canvas = tk.Canvas(canvas_frame, bg=BACKGROUND_COLOR, height=GAME_HEIGHT, width=GAME_WIDTH,
                  highlightthickness=0)
canvas.pack()

# Add grid pattern to canvas for better visual appeal
def draw_grid():
    for i in range(0, GAME_WIDTH, SPACE_SIZE):
        canvas.create_line(i, 0, i, GAME_HEIGHT, fill=GRID_COLOR, width=1)
    for i in range(0, GAME_HEIGHT, SPACE_SIZE):
        canvas.create_line(0, i, GAME_WIDTH, i, fill=GRID_COLOR, width=1)

draw_grid()

# Add welcome screen
def show_welcome_screen():
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 - 60,
                       font=('Arial', 28, 'bold'), text="🐍", fill="#4CAF50")
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 - 20,
                       font=('Arial', 16, 'bold'), text="Welcome to Snake Game!", fill="#FFFFFF")
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 + 10,
                       font=('Arial', 12), text="Use arrow keys to control the snake", fill="#CCCCCC")
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 + 30,
                       font=('Arial', 12), text="Eat the red food to grow and score!", fill="#CCCCCC")
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 + 60,
                       font=('Arial', 14, 'bold'), text="Press Enter or click Start Game to begin", fill="#4CAF50")

show_welcome_screen()

# Create a frame for the buttons with better styling
button_frame = tk.Frame(window, bg=BACKGROUND_COLOR)
button_frame.pack(pady=10)

# Modern button styling
button_style = {
    'font': ('Arial', 14, 'bold'),
    'bg': BUTTON_COLOR,
    'fg': 'white',
    'activebackground': BUTTON_HOVER_COLOR,
    'activeforeground': 'white',
    'relief': 'flat',
    'bd': 0,
    'padx': 20,
    'pady': 8,
    'cursor': 'hand2'
}

# Add a start button inside the frame
start_button = tk.Button(button_frame, text="🎮 Start Game", command=start_game, **button_style)
start_button.pack(side=tk.LEFT, padx=5)

# Add a show history button inside the frame
history_button = tk.Button(button_frame, text="📊 Show History", command=show_history, **button_style)
history_button.pack(side=tk.LEFT, padx=5)

# Sound control functions
def toggle_sound():
    enabled = sound_manager.toggle_sounds()
    sound_button.config(text="🔊 Sound ON" if enabled else "🔇 Sound OFF")
    if enabled:
        sound_manager.play_sound('turn')  # Test sound

def show_sound_settings():
    settings_window = tk.Toplevel(window)
    settings_window.title("🔊 Sound Settings")
    settings_window.geometry("300x200")
    settings_window.configure(bg=BACKGROUND_COLOR)
    settings_window.resizable(False, False)
    settings_window.transient(window)
    settings_window.grab_set()

    # Title
    title_label = tk.Label(settings_window, text="🔊 SOUND SETTINGS",
                          font=('Arial', 16, 'bold'),
                          fg="#4CAF50", bg=BACKGROUND_COLOR)
    title_label.pack(pady=10)

    # Volume control
    volume_frame = tk.Frame(settings_window, bg=BACKGROUND_COLOR)
    volume_frame.pack(pady=10)

    tk.Label(volume_frame, text="Volume:", font=('Arial', 12),
             fg=SCORE_COLOR, bg=BACKGROUND_COLOR).pack(side=tk.LEFT)

    def update_volume(val):
        volume = float(val) / 100
        sound_manager.set_volume(volume)
        volume_label.config(text=f"{int(float(val))}%")

    volume_scale = tk.Scale(volume_frame, from_=0, to=100, orient=tk.HORIZONTAL,
                           command=update_volume, bg=BACKGROUND_COLOR,
                           fg=SCORE_COLOR, highlightthickness=0,
                           troughcolor=ACCENT_COLOR, activebackground=BUTTON_COLOR)
    volume_scale.set(int(sound_manager.volume * 100))
    volume_scale.pack(side=tk.LEFT, padx=10)

    volume_label = tk.Label(volume_frame, text=f"{int(sound_manager.volume * 100)}%",
                           font=('Arial', 10), fg=SCORE_COLOR, bg=BACKGROUND_COLOR)
    volume_label.pack(side=tk.LEFT)

    # Sound toggle
    toggle_frame = tk.Frame(settings_window, bg=BACKGROUND_COLOR)
    toggle_frame.pack(pady=10)

    sound_toggle_button = tk.Button(toggle_frame,
                                   text="🔊 Sound ON" if sound_manager.sounds_enabled else "🔇 Sound OFF",
                                   command=lambda: [toggle_sound(),
                                                   sound_toggle_button.config(text="🔊 Sound ON" if sound_manager.sounds_enabled else "🔇 Sound OFF")],
                                   **button_style)
    sound_toggle_button.pack(pady=5)

    # Test sounds
    test_frame = tk.Frame(settings_window, bg=BACKGROUND_COLOR)
    test_frame.pack(pady=10)

    tk.Label(test_frame, text="Test Sounds:", font=('Arial', 10),
             fg=SCORE_COLOR, bg=BACKGROUND_COLOR).pack()

    test_buttons_frame = tk.Frame(test_frame, bg=BACKGROUND_COLOR)
    test_buttons_frame.pack()

    test_button_style = button_style.copy()
    test_button_style['font'] = ('Arial', 8)
    test_button_style['padx'] = 5
    test_button_style['pady'] = 2

    tk.Button(test_buttons_frame, text="Eat",
              command=lambda: sound_manager.play_sound('eat'), **test_button_style).pack(side=tk.LEFT, padx=2)
    tk.Button(test_buttons_frame, text="Move",
              command=lambda: sound_manager.play_sound('move'), **test_button_style).pack(side=tk.LEFT, padx=2)
    tk.Button(test_buttons_frame, text="Turn",
              command=lambda: sound_manager.play_sound('turn'), **test_button_style).pack(side=tk.LEFT, padx=2)

    # Close button
    close_button = tk.Button(settings_window, text="✖ Close",
                           command=settings_window.destroy, **button_style)
    close_button.pack(pady=10)

# Add sound control button
sound_button = tk.Button(button_frame, text="🔊 Sound ON" if sound_manager.sounds_enabled else "🔇 Sound OFF",
                        command=show_sound_settings, **button_style)
sound_button.pack(side=tk.LEFT, padx=5)


window.update()

window_width = window.winfo_width()
window_height = window.winfo_height()
screen_width = window.winfo_screenwidth()
screen_height = window.winfo_screenheight()

x = int((screen_width/2) - (window_width/2))
y = int((screen_height/2) - (window_height/2))

window.geometry(f"{window_width}x{window_height}+{x}+{y}")

window.bind('<Left>', lambda event: change_direction('left'))
window.bind('<Right>', lambda event: change_direction('right'))
window.bind('<Up>', lambda event: change_direction('up'))
window.bind('<Down>', lambda event: change_direction('down'))
# Bind the Enter key to the start_game function
window.bind('<Return>', lambda event: start_game())

# Do not start the game immediately, wait for button click
# snake = Snake()
# food = Food()
# next_turn(snake, food)

window.mainloop()
