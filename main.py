import tkinter as tk
from tkinter import ttk
import random
import datetime # Import datetime module
import pygame.mixer # Import pygame mixer for sound

# Game constants
GAME_WIDTH = 400 # Slightly increased for better proportions
GAME_HEIGHT = 400 # Slightly increased for better proportions
SPEED = 150 # Increased from 100 to slow down the game/crawl rhythm
SPACE_SIZE = 20 # Adjusted for better grid
BODY_PARTS = 3

# Modern color scheme
SNAKE_HEAD_COLOR = "#4CAF50"  # Material Green
SNAKE_BODY_COLOR = "#66BB6A"  # Lighter Green
FOOD_COLOR = "#FF5722"        # Material Deep Orange
BACKGROUND_COLOR = "#1A1A2E"  # Dark Blue-Purple
GRID_COLOR = "#16213E"        # Slightly lighter for grid
SCORE_COLOR = "#FFFFFF"       # White
ACCENT_COLOR = "#0F3460"      # Blue accent
BUTTON_COLOR = "#E94560"      # Pink-Red for buttons
BUTTON_HOVER_COLOR = "#C73650" # Darker pink-red for hover

# Global variables
score = 0
direction = 'down'
score_history = [] # List to store score history
game_start_time = None # Variable to store the start time of the current game

# Initialize mixer and load sounds
pygame.mixer.init()
try:
    eat_sound = pygame.mixer.Sound("eat.wav") # Make sure you have eat.wav
    gameover_sound = pygame.mixer.Sound("gameover.wav") # Make sure you have gameover.wav
    crawl_sound = pygame.mixer.Sound("crawl.wav") # Make sure you have crawl.wav
except pygame.error as e:
    print(f"Could not load sound files: {e}")
    eat_sound = None
    gameover_sound = None
    crawl_sound = None


class Snake:
    def __init__(self):
        self.body_size = BODY_PARTS
        self.coordinates = []
        self.squares = []

        for i in range(0, BODY_PARTS):
            self.coordinates.append([0, 0])

        for i, (x, y) in enumerate(self.coordinates):
            # Head is different color from body
            color = SNAKE_HEAD_COLOR if i == 0 else SNAKE_BODY_COLOR
            square = canvas.create_rectangle(x, y, x + SPACE_SIZE, y + SPACE_SIZE,
                                           fill=color, outline="#2E7D32", width=1, tag="snake")
            self.squares.append(square)

class Food:
    def __init__(self):
        x = random.randint(0, (GAME_WIDTH // SPACE_SIZE) - 1) * SPACE_SIZE
        y = random.randint(0, (GAME_HEIGHT // SPACE_SIZE) - 1) * SPACE_SIZE
        self.coordinates = [x, y]
        # Create a more attractive food with gradient effect
        canvas.create_oval(x + 2, y + 2, x + SPACE_SIZE - 2, y + SPACE_SIZE - 2,
                          fill=FOOD_COLOR, outline="#D84315", width=2, tag="food")
        # Add a small highlight for 3D effect
        canvas.create_oval(x + 5, y + 5, x + 10, y + 10,
                          fill="#FFAB91", outline="", tag="food")

def next_turn(snake, food):
    x, y = snake.coordinates[0]

    if direction == "up":
        y -= SPACE_SIZE
    elif direction == "down":
        y += SPACE_SIZE
    elif direction == "left":
        x -= SPACE_SIZE
    elif direction == "right":
        x += SPACE_SIZE

    snake.coordinates.insert(0, [x, y])

    # Create new head with head color and update previous head to body color
    square = canvas.create_rectangle(x, y, x + SPACE_SIZE, y + SPACE_SIZE,
                                   fill=SNAKE_HEAD_COLOR, outline="#2E7D32", width=1)
    snake.squares.insert(0, square)

    # Update the previous head to body color if it exists
    if len(snake.squares) > 1:
        canvas.itemconfig(snake.squares[1], fill=SNAKE_BODY_COLOR)

    if x == food.coordinates[0] and y == food.coordinates[1]:
        global score
        score += 1
        label.config(text="Score:{}".format(score))
        canvas.delete("food")
        food = Food()
        # Play eat sound
        if eat_sound:
            eat_sound.play()
    else:
        del snake.coordinates[-1]
        canvas.delete(snake.squares[-1])
        del snake.squares[-1]

    if check_collisions(snake):
        game_over()
    else:
        # Play crawl sound
        if crawl_sound:
            crawl_sound.play()
        window.after(SPEED, next_turn, snake, food) # SPEED controls the delay

def change_direction(new_direction):
    global direction
    if new_direction == 'left':
        if direction != 'right':
            direction = new_direction
    elif new_direction == 'right':
        if direction != 'left':
            direction = new_direction
    elif new_direction == 'up':
        if direction != 'down':
            direction = new_direction
    elif new_direction == 'down':
        if direction != 'up':
            direction = new_direction

def check_collisions(snake):
    x, y = snake.coordinates[0]

    if x < 0 or x >= GAME_WIDTH or y < 0 or y >= GAME_HEIGHT:
        return True

    for body_part in snake.coordinates[1:]:
        if x == body_part[0] and y == body_part[1]:
            return True

    return False

def game_over():
    # Play game over sound
    if gameover_sound:
        gameover_sound.play()

    canvas.delete(tk.ALL)

    # Create a more attractive game over screen
    # Background overlay
    canvas.create_rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT,
                           fill="#000000", stipple="gray50", tag="gameover")

    # Game Over text with shadow effect
    canvas.create_text(canvas.winfo_width()/2 + 2, canvas.winfo_height()/2 - 48,
                       font=('Arial', 32, 'bold'), text="GAME OVER", fill="#333333", tag="gameover")
    canvas.create_text(canvas.winfo_width()/2, canvas.winfo_height()/2 - 50,
                       font=('Arial', 32, 'bold'), text="GAME OVER", fill="#FF5722", tag="gameover")

    # Score display
    canvas.create_text(canvas.winfo_width()/2, canvas.winfo_height()/2 - 10,
                       font=('Arial', 18, 'bold'), text=f"Final Score: {score}",
                       fill="#FFFFFF", tag="gameover")

    # Instructions
    canvas.create_text(canvas.winfo_width()/2, canvas.winfo_height()/2 + 20,
                       font=('Arial', 12), text="Press Enter or click Start Game to play again",
                       fill="#CCCCCC", tag="gameover")

    # Show the button frame again
    button_frame.pack() # Pack the frame containing the buttons
    # Record the score history
    game_end_time = datetime.datetime.now() # Record end time
    score_history.append({'score': score, 'start_time': game_start_time, 'end_time': game_end_time})

def start_game():
    global snake, food, score, direction, game_start_time
    # Reset score and direction
    score = 0
    direction = 'down'
    label.config(text="Score: {}".format(score))

    # Clear previous game elements if any
    canvas.delete(tk.ALL)

    # Redraw the grid
    draw_grid()

    # Hide the button frame
    button_frame.pack_forget() # Unpack the frame containing the buttons

    # Record the start time
    game_start_time = datetime.datetime.now()

    # Create new snake and food
    snake = Snake()
    food = Food()

    # Start the game loop
    next_turn(snake, food)

def show_history():
    history_window = tk.Toplevel(window)
    history_window.title("📊 Game History")
    history_window.geometry("500x400")
    history_window.configure(bg=BACKGROUND_COLOR)
    history_window.resizable(False, False)

    # Center the history window
    history_window.transient(window)
    history_window.grab_set()

    # Title for history window
    title_frame = tk.Frame(history_window, bg=BACKGROUND_COLOR)
    title_frame.pack(pady=10)

    title_label = tk.Label(title_frame, text="📊 GAME HISTORY",
                          font=('Arial', 20, 'bold'),
                          fg="#4CAF50", bg=BACKGROUND_COLOR)
    title_label.pack()

    # Create a frame for the text widget with scrollbar
    text_frame = tk.Frame(history_window, bg=BACKGROUND_COLOR)
    text_frame.pack(expand=True, fill="both", padx=20, pady=10)

    # Scrollbar
    scrollbar = tk.Scrollbar(text_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # Text widget with better styling
    history_text = tk.Text(text_frame, wrap="word",
                          bg="#2A2A3E", fg="#FFFFFF",
                          font=('Consolas', 11),
                          yscrollcommand=scrollbar.set,
                          relief='flat', bd=0,
                          padx=15, pady=15)
    history_text.pack(expand=True, fill="both")

    scrollbar.config(command=history_text.yview)

    if not score_history:
        history_text.insert(tk.END, "🎮 No games played yet!\n\n")
        history_text.insert(tk.END, "Start playing to see your game history here.")
    else:
        history_text.insert(tk.END, f"🏆 Total Games Played: {len(score_history)}\n")
        best_score = max(game['score'] for game in score_history)
        history_text.insert(tk.END, f"🥇 Best Score: {best_score}\n")
        avg_score = sum(game['score'] for game in score_history) / len(score_history)
        history_text.insert(tk.END, f"📈 Average Score: {avg_score:.1f}\n\n")
        history_text.insert(tk.END, "=" * 50 + "\n\n")

        for i, game_data in enumerate(score_history):
            start_str = game_data['start_time'].strftime("%Y-%m-%d %H:%M:%S") if game_data['start_time'] else "N/A"
            end_str = game_data['end_time'].strftime("%Y-%m-%d %H:%M:%S") if game_data['end_time'] else "N/A"

            # Calculate game duration
            if game_data['start_time'] and game_data['end_time']:
                duration = game_data['end_time'] - game_data['start_time']
                duration_str = str(duration).split('.')[0]  # Remove microseconds
            else:
                duration_str = "N/A"

            history_text.insert(tk.END, f"🎯 Game #{i+1}\n")
            history_text.insert(tk.END, f"   Score: {game_data['score']} points\n")
            history_text.insert(tk.END, f"   Started: {start_str}\n")
            history_text.insert(tk.END, f"   Ended: {end_str}\n")
            history_text.insert(tk.END, f"   Duration: {duration_str}\n")
            history_text.insert(tk.END, "-" * 40 + "\n\n")

    history_text.config(state="disabled")

    # Close button
    close_button = tk.Button(history_window, text="✖ Close",
                           command=history_window.destroy,
                           **button_style)
    close_button.pack(pady=10)

# Setup the main window
window = tk.Tk()
window.title("🐍 Snake Game - Modern Edition")
window.resizable(False, False)
window.configure(bg=BACKGROUND_COLOR)

# Create a title frame
title_frame = tk.Frame(window, bg=BACKGROUND_COLOR)
title_frame.pack(pady=10)

# Add a stylish title
title_label = tk.Label(title_frame, text="🐍 SNAKE GAME",
                      font=('Arial', 24, 'bold'),
                      fg="#4CAF50", bg=BACKGROUND_COLOR)
title_label.pack()

# Score label with better styling
label = tk.Label(window, text="Score: {}".format(score),
                font=('Arial', 18, 'bold'),
                fg=SCORE_COLOR, bg=BACKGROUND_COLOR,
                pady=5)
label.pack()

# Create canvas with border effect
canvas_frame = tk.Frame(window, bg="#2E7D32", padx=3, pady=3)
canvas_frame.pack(pady=5)

canvas = tk.Canvas(canvas_frame, bg=BACKGROUND_COLOR, height=GAME_HEIGHT, width=GAME_WIDTH,
                  highlightthickness=0)
canvas.pack()

# Add grid pattern to canvas for better visual appeal
def draw_grid():
    for i in range(0, GAME_WIDTH, SPACE_SIZE):
        canvas.create_line(i, 0, i, GAME_HEIGHT, fill=GRID_COLOR, width=1)
    for i in range(0, GAME_HEIGHT, SPACE_SIZE):
        canvas.create_line(0, i, GAME_WIDTH, i, fill=GRID_COLOR, width=1)

draw_grid()

# Add welcome screen
def show_welcome_screen():
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 - 60,
                       font=('Arial', 28, 'bold'), text="🐍", fill="#4CAF50")
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 - 20,
                       font=('Arial', 16, 'bold'), text="Welcome to Snake Game!", fill="#FFFFFF")
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 + 10,
                       font=('Arial', 12), text="Use arrow keys to control the snake", fill="#CCCCCC")
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 + 30,
                       font=('Arial', 12), text="Eat the red food to grow and score!", fill="#CCCCCC")
    canvas.create_text(GAME_WIDTH/2, GAME_HEIGHT/2 + 60,
                       font=('Arial', 14, 'bold'), text="Press Enter or click Start Game to begin", fill="#4CAF50")

show_welcome_screen()

# Create a frame for the buttons with better styling
button_frame = tk.Frame(window, bg=BACKGROUND_COLOR)
button_frame.pack(pady=10)

# Modern button styling
button_style = {
    'font': ('Arial', 14, 'bold'),
    'bg': BUTTON_COLOR,
    'fg': 'white',
    'activebackground': BUTTON_HOVER_COLOR,
    'activeforeground': 'white',
    'relief': 'flat',
    'bd': 0,
    'padx': 20,
    'pady': 8,
    'cursor': 'hand2'
}

# Add a start button inside the frame
start_button = tk.Button(button_frame, text="🎮 Start Game", command=start_game, **button_style)
start_button.pack(side=tk.LEFT, padx=5)

# Add a show history button inside the frame
history_button = tk.Button(button_frame, text="📊 Show History", command=show_history, **button_style)
history_button.pack(side=tk.LEFT, padx=5)


window.update()

window_width = window.winfo_width()
window_height = window.winfo_height()
screen_width = window.winfo_screenwidth()
screen_height = window.winfo_screenheight()

x = int((screen_width/2) - (window_width/2))
y = int((screen_height/2) - (window_height/2))

window.geometry(f"{window_width}x{window_height}+{x}+{y}")

window.bind('<Left>', lambda event: change_direction('left'))
window.bind('<Right>', lambda event: change_direction('right'))
window.bind('<Up>', lambda event: change_direction('up'))
window.bind('<Down>', lambda event: change_direction('down'))
# Bind the Enter key to the start_game function
window.bind('<Return>', lambda event: start_game())

# Do not start the game immediately, wait for button click
# snake = Snake()
# food = Food()
# next_turn(snake, food)

window.mainloop()
