#!/usr/bin/env python3
"""
Sound test script for the Snake game
This script allows you to test individual sound effects
"""

import pygame.mixer
import time
import sys

def init_sound():
    """Initialize pygame mixer"""
    try:
        pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
        return True
    except pygame.error as e:
        print(f"Could not initialize sound: {e}")
        return False

def load_sound(filename):
    """Load a sound file"""
    try:
        sound = pygame.mixer.Sound(filename)
        return sound
    except (pygame.error, FileNotFoundError) as e:
        print(f"Could not load {filename}: {e}")
        return None

def test_sound(sound_name, filename):
    """Test a specific sound"""
    print(f"Testing {sound_name}...")
    sound = load_sound(filename)
    if sound:
        sound.play()
        time.sleep(1)  # Wait for sound to finish
        print(f"✓ {sound_name} played successfully")
    else:
        print(f"✗ Failed to play {sound_name}")

def main():
    """Main function"""
    print("🔊 Snake Game Sound Test")
    print("=" * 30)
    
    if not init_sound():
        print("Failed to initialize sound system")
        return
    
    sounds = [
        ("Eat Sound", "eat.wav"),
        ("Game Over Sound", "gameover.wav"),
        ("Move Sound", "move.wav"),
        ("Turn Sound", "turn.wav"),
        ("Start Sound", "start.wav"),
        ("Crawl Sound", "crawl.wav")
    ]
    
    print("Testing all sounds...")
    print()
    
    for sound_name, filename in sounds:
        test_sound(sound_name, filename)
        time.sleep(0.5)  # Brief pause between sounds
    
    print()
    print("Sound test completed!")
    print()
    print("Interactive mode - Press keys to test sounds:")
    print("1 - Eat sound")
    print("2 - Game over sound")
    print("3 - Move sound")
    print("4 - Turn sound")
    print("5 - Start sound")
    print("6 - Crawl sound")
    print("q - Quit")
    
    # Load all sounds
    eat_sound = load_sound("eat.wav")
    gameover_sound = load_sound("gameover.wav")
    move_sound = load_sound("move.wav")
    turn_sound = load_sound("turn.wav")
    start_sound = load_sound("start.wav")
    crawl_sound = load_sound("crawl.wav")
    
    while True:
        try:
            key = input("\nPress a key (1-6, q to quit): ").strip().lower()
            
            if key == 'q':
                break
            elif key == '1' and eat_sound:
                print("Playing eat sound...")
                eat_sound.play()
            elif key == '2' and gameover_sound:
                print("Playing game over sound...")
                gameover_sound.play()
            elif key == '3' and move_sound:
                print("Playing move sound...")
                move_sound.play()
            elif key == '4' and turn_sound:
                print("Playing turn sound...")
                turn_sound.play()
            elif key == '5' and start_sound:
                print("Playing start sound...")
                start_sound.play()
            elif key == '6' and crawl_sound:
                print("Playing crawl sound...")
                crawl_sound.play()
            else:
                print("Invalid key. Use 1-6 or q to quit.")
                
        except KeyboardInterrupt:
            break
        except EOFError:
            break
    
    print("\nGoodbye!")

if __name__ == "__main__":
    main()
